import React from 'react';

interface RRData {
  entry: number;
  stop: number;
  target: number;
  direction: 'long' | 'short';
  positionSize?: number;
  timeStart: string;
  timeEnd: string;
}

interface RiskRewardToolProps {
  drawing: any;
  index: number;
  onEdit?: (index: number) => void;
  onDelete?: (index: number) => void;
  isDraggable?: boolean;
  onStartDrag?: (event: any, drawingIndex: number, dragType: 'tool' | 'handle', pointIndex?: number) => void;
  onUpdateDrawing?: (index: number, updatedDrawing: any) => void;
}

// Helper functions for RR calculations
const calculateRiskReward = (rrData: RRData) => {
  const risk = Math.abs(rrData.entry - rrData.stop);
  const reward = Math.abs(rrData.target - rrData.entry);
  const rr = risk > 0 ? reward / risk : 0;

  const dollarRisk = rrData.positionSize ? rrData.positionSize * risk : null;
  const dollarReward = rrData.positionSize ? rrData.positionSize * reward : null;

  return { risk, reward, rr, dollarRisk, dollarReward };
};

const convertDrawingToRRData = (drawing: any): RRData | null => {
  if (!drawing.rrData) {
    // Legacy conversion from points-based structure
    if (!drawing.points || drawing.points.length < 3) return null;

    const [entryPoint, stopPoint, targetPoint] = drawing.points;
    const timeStart = new Date().toISOString();
    const timeEnd = new Date(Date.now() + 6 * 60 * 60 * 1000).toISOString(); // 6 hours later

    return {
      entry: entryPoint.price,
      stop: stopPoint.price,
      target: targetPoint.price,
      direction: targetPoint.price > entryPoint.price ? 'long' : 'short',
      positionSize: drawing.positionSize || undefined,
      timeStart,
      timeEnd
    };
  }

  return drawing.rrData;
};

export const createRiskRewardSeries = (
  drawing: any,
  index: number,
  onEdit?: (index: number) => void,
  isDraggable: boolean = true,
  onStartDrag?: (event: any, drawingIndex: number, dragType: 'tool' | 'handle', pointIndex?: number) => void,
  onUpdateDrawing?: (index: number, updatedDrawing: any) => void
) => {
  const rrData = convertDrawingToRRData(drawing);
  if (!rrData) return [];

  const style = drawing.style || {};
  const { risk, reward, rr, dollarRisk, dollarReward } = calculateRiskReward(rrData);

  const series = [];

  // Simple time boundaries - make it wide enough to see
  const leftTime = rrData.timeStart;
  const rightTime = rrData.timeEnd;

  // Signature green reward zone (top) - solid fill
  const greenBoxData = [
    [leftTime, rrData.entry],
    [rightTime, rrData.entry],
    [rightTime, rrData.target],
    [leftTime, rrData.target],
    [leftTime, rrData.entry]
  ];

  series.push({
    name: `RR_${index}_green_box`,
    type: 'line',
    coordinateSystem: 'cartesian2d',
    data: greenBoxData,
    lineStyle: {
      color: '#00e7b6',
      width: 0
    },
    areaStyle: {
      color: '#00e7b6' // Signature green solid fill
    },
    symbol: 'none',
    z: 999,
    silent: false,
    animation: false,
    drawingIndex: index,
    handleType: 'green_box',
    isDraggableHandle: true
  });

  // Dark red risk zone (bottom) - solid fill
  const redBoxData = [
    [leftTime, rrData.entry],
    [rightTime, rrData.entry],
    [rightTime, rrData.stop],
    [leftTime, rrData.stop],
    [leftTime, rrData.entry]
  ];

  series.push({
    name: `RR_${index}_red_box`,
    type: 'line',
    coordinateSystem: 'cartesian2d',
    data: redBoxData,
    lineStyle: {
      color: '#8B0000',
      width: 0
    },
    areaStyle: {
      color: '#8B0000' // Dark red solid fill
    },
    symbol: 'none',
    z: 999,
    silent: false,
    animation: false,
    drawingIndex: index,
    handleType: 'red_box',
    isDraggableHandle: true
  });

  // Subtle entry line (grey instead of bright white)
  series.push({
    name: `RR_${index}_entry_line`,
    type: 'line',
    coordinateSystem: 'cartesian2d',
    data: [[leftTime, rrData.entry], [rightTime, rrData.entry]],
    lineStyle: {
      color: '#9CA3AF',
      width: 1,
      type: 'solid'
    },
    symbol: 'none',
    z: 1001,
    silent: false,
    animation: false
  });

  // Add clean, minimal draggable handles if enabled
  if (isDraggable) {
    const centerTime = new Date((new Date(leftTime).getTime() + new Date(rightTime).getTime()) / 2).toISOString();

    // Target handle (top of green box - drag to expand/shrink vertically)
    series.push({
      name: `RR_${index}_target_handle`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[centerTime, rrData.target]],
      symbol: 'circle',
      symbolSize: 8,
      itemStyle: {
        color: '#00e7b6',
        borderColor: '#ffffff',
        borderWidth: 1,
        shadowColor: 'rgba(0, 0, 0, 0.2)',
        shadowBlur: 2
      },
      z: 2001,
      silent: false,
      animation: false,
      cursor: 'ns-resize',
      drawingIndex: index,
      handleType: 'target',
      drawingType: 'rr_level_handle',
      isDraggableHandle: true
    });

    // Entry handle (center - drag to move entire box)
    series.push({
      name: `RR_${index}_entry_handle`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[centerTime, rrData.entry]],
      symbol: 'circle',
      symbolSize: 10,
      itemStyle: {
        color: '#ffffff',
        borderColor: '#141414',
        borderWidth: 1,
        shadowColor: 'rgba(0, 0, 0, 0.3)',
        shadowBlur: 3
      },
      z: 2002,
      silent: false,
      animation: false,
      cursor: 'move',
      drawingIndex: index,
      handleType: 'entry',
      drawingType: 'rr_level_handle',
      isDraggableHandle: true
    });

    // Stop handle (bottom of red box - drag to expand/shrink vertically)
    series.push({
      name: `RR_${index}_stop_handle`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[centerTime, rrData.stop]],
      symbol: 'circle',
      symbolSize: 8,
      itemStyle: {
        color: '#ff4757',
        borderColor: '#ffffff',
        borderWidth: 1,
        shadowColor: 'rgba(0, 0, 0, 0.2)',
        shadowBlur: 2
      },
      z: 2001,
      silent: false,
      animation: false,
      cursor: 'ns-resize',
      drawingIndex: index,
      handleType: 'stop',
      drawingType: 'rr_level_handle',
      isDraggableHandle: true
    });

    // Left resize handle (expand/shrink horizontally)
    series.push({
      name: `RR_${index}_left_resize`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[leftTime, rrData.entry]],
      symbol: 'rect',
      symbolSize: 6,
      itemStyle: {
        color: '#ffffff',
        borderColor: '#141414',
        borderWidth: 1,
        shadowColor: 'rgba(0, 0, 0, 0.2)',
        shadowBlur: 2
      },
      z: 2000,
      silent: false,
      animation: false,
      cursor: 'ew-resize',
      drawingIndex: index,
      handleType: 'resize_left',
      drawingType: 'rr_resize_handle',
      isDraggableHandle: true
    });

    // Right resize handle (expand/shrink horizontally)
    series.push({
      name: `RR_${index}_right_resize`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[rightTime, rrData.entry]],
      symbol: 'rect',
      symbolSize: 6,
      itemStyle: {
        color: '#ffffff',
        borderColor: '#141414',
        borderWidth: 1,
        shadowColor: 'rgba(0, 0, 0, 0.2)',
        shadowBlur: 2
      },
      z: 2000,
      silent: false,
      animation: false,
      cursor: 'ew-resize',
      drawingIndex: index,
      handleType: 'resize_right',
      drawingType: 'rr_resize_handle',
      isDraggableHandle: true
    });

    // Corner handles for diagonal resizing
    // Top-left corner
    series.push({
      name: `RR_${index}_corner_tl`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[leftTime, rrData.target]],
      symbol: 'rect',
      symbolSize: 5,
      itemStyle: {
        color: '#00e7b6',
        borderColor: '#ffffff',
        borderWidth: 1
      },
      z: 2001,
      silent: false,
      animation: false,
      cursor: 'nw-resize',
      drawingIndex: index,
      handleType: 'corner_tl',
      drawingType: 'rr_corner_handle',
      isDraggableHandle: true
    });

    // Top-right corner
    series.push({
      name: `RR_${index}_corner_tr`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[rightTime, rrData.target]],
      symbol: 'rect',
      symbolSize: 5,
      itemStyle: {
        color: '#00e7b6',
        borderColor: '#ffffff',
        borderWidth: 1
      },
      z: 2001,
      silent: false,
      animation: false,
      cursor: 'ne-resize',
      drawingIndex: index,
      handleType: 'corner_tr',
      drawingType: 'rr_corner_handle',
      isDraggableHandle: true
    });

    // Bottom-left corner
    series.push({
      name: `RR_${index}_corner_bl`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[leftTime, rrData.stop]],
      symbol: 'rect',
      symbolSize: 5,
      itemStyle: {
        color: '#8B0000',
        borderColor: '#ffffff',
        borderWidth: 1
      },
      z: 2001,
      silent: false,
      animation: false,
      cursor: 'sw-resize',
      drawingIndex: index,
      handleType: 'corner_bl',
      drawingType: 'rr_corner_handle',
      isDraggableHandle: true
    });

    // Bottom-right corner
    series.push({
      name: `RR_${index}_corner_br`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[rightTime, rrData.stop]],
      symbol: 'rect',
      symbolSize: 5,
      itemStyle: {
        color: '#8B0000',
        borderColor: '#ffffff',
        borderWidth: 1
      },
      z: 2001,
      silent: false,
      animation: false,
      cursor: 'se-resize',
      drawingIndex: index,
      handleType: 'corner_br',
      drawingType: 'rr_corner_handle',
      isDraggableHandle: true
    });
  }



  return series;
};

// Helper function to create a new RR tool
export const createNewRRTool = (
  entryPrice: number,
  entryTime: string,
  direction: 'long' | 'short' = 'long',
  positionSize?: number
): any => {
  // Make bigger default boxes for better visibility
  const stopOffset = direction === 'long' ? entryPrice * 0.05 : entryPrice * 0.05; // 5% default (bigger)
  const targetOffset = direction === 'long' ? entryPrice * 0.10 : entryPrice * 0.10; // 10% default (bigger)

  const stop = direction === 'long' ? entryPrice - stopOffset : entryPrice + stopOffset;
  const target = direction === 'long' ? entryPrice + targetOffset : entryPrice - targetOffset;

  const timeStart = entryTime;
  // Make it wider - 4 hours for better visibility
  const timeEnd = new Date(new Date(entryTime).getTime() + 4 * 60 * 60 * 1000).toISOString(); // 4 hours later

  return {
    type: 'rr',
    id: Date.now(),
    rrData: {
      entry: entryPrice,
      stop,
      target,
      direction,
      positionSize,
      timeStart,
      timeEnd
    },
    style: {
      entryColor: '#ffffff',
      riskColor: '#ff4757',
      rewardColor: '#00e7b6',
      width: 2,
      showLabels: true,
      showRatio: true
    }
  };
};

// Helper function to update RR tool data
export const updateRRTool = (
  drawing: any,
  updates: Partial<RRData>
): any => {
  const currentRRData = convertDrawingToRRData(drawing);
  if (!currentRRData) return drawing;

  const updatedRRData = { ...currentRRData, ...updates };

  // Auto-detect direction if entry, stop, or target changed
  if (updates.entry !== undefined || updates.stop !== undefined || updates.target !== undefined) {
    updatedRRData.direction = updatedRRData.target > updatedRRData.entry ? 'long' : 'short';
  }

  return {
    ...drawing,
    rrData: updatedRRData
  };
};

// Helper function to handle drag updates for RR tools
export const handleRRDrag = (
  drawing: any,
  handleType: string,
  newPrice: number,
  newTime?: string
): any => {
  const rrData = convertDrawingToRRData(drawing);
  if (!rrData) return drawing;

  switch (handleType) {
    case 'entry':
      // Move all levels together (maintain relative distances)
      const entryDelta = newPrice - rrData.entry;
      return updateRRTool(drawing, {
        entry: newPrice,
        stop: rrData.stop + entryDelta,
        target: rrData.target + entryDelta
      });

    case 'stop':
      // Move only stop level
      return updateRRTool(drawing, { stop: newPrice });

    case 'target':
      // Move only target level
      return updateRRTool(drawing, { target: newPrice });

    case 'green_box':
      // Dragging green box - adjust target level
      return updateRRTool(drawing, { target: newPrice });

    case 'red_box':
      // Dragging red box - adjust stop level
      return updateRRTool(drawing, { stop: newPrice });

    case 'resize_left':
      // Adjust left time boundary
      if (newTime) {
        return updateRRTool(drawing, {
          timeStart: newTime < rrData.timeEnd ? newTime : rrData.timeStart
        });
      }
      break;

    case 'resize_right':
      // Adjust right time boundary
      if (newTime) {
        return updateRRTool(drawing, {
          timeEnd: newTime > rrData.timeStart ? newTime : rrData.timeEnd
        });
      }
      break;

    case 'corner_tl':
      // Top-left corner: adjust both target and left time
      const updates_tl: any = { target: newPrice };
      if (newTime) updates_tl.timeStart = newTime < rrData.timeEnd ? newTime : rrData.timeStart;
      return updateRRTool(drawing, updates_tl);

    case 'corner_tr':
      // Top-right corner: adjust both target and right time
      const updates_tr: any = { target: newPrice };
      if (newTime) updates_tr.timeEnd = newTime > rrData.timeStart ? newTime : rrData.timeEnd;
      return updateRRTool(drawing, updates_tr);

    case 'corner_bl':
      // Bottom-left corner: adjust both stop and left time
      const updates_bl: any = { stop: newPrice };
      if (newTime) updates_bl.timeStart = newTime < rrData.timeEnd ? newTime : rrData.timeStart;
      return updateRRTool(drawing, updates_bl);

    case 'corner_br':
      // Bottom-right corner: adjust both stop and right time
      const updates_br: any = { stop: newPrice };
      if (newTime) updates_br.timeEnd = newTime > rrData.timeStart ? newTime : rrData.timeEnd;
      return updateRRTool(drawing, updates_br);

    default:
      return drawing;
  }

  return drawing;
};

const RiskRewardTool: React.FC<RiskRewardToolProps> = ({ drawing, index, onEdit, onDelete }) => {
  // This component is mainly for type definitions and utility functions
  // The actual rendering is handled by createRiskRewardSeries function
  return null;
};

export default RiskRewardTool;
