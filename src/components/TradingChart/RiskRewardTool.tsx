import React from 'react';

interface RRData {
  entry: number;
  stop: number;
  target: number;
  direction: 'long' | 'short';
  positionSize?: number;
  timeStart: string;
  timeEnd: string;
}

interface RiskRewardToolProps {
  drawing: any;
  index: number;
  onEdit?: (index: number) => void;
  onDelete?: (index: number) => void;
  isDraggable?: boolean;
  onStartDrag?: (event: any, drawingIndex: number, dragType: 'tool' | 'handle', pointIndex?: number) => void;
  onUpdateDrawing?: (index: number, updatedDrawing: any) => void;
}

// Helper functions for RR calculations
const calculateRiskReward = (rrData: RRData) => {
  const risk = Math.abs(rrData.entry - rrData.stop);
  const reward = Math.abs(rrData.target - rrData.entry);
  const rr = risk > 0 ? reward / risk : 0;

  const dollarRisk = rrData.positionSize ? rrData.positionSize * risk : null;
  const dollarReward = rrData.positionSize ? rrData.positionSize * reward : null;

  return { risk, reward, rr, dollarRisk, dollarReward };
};

const convertDrawingToRRData = (drawing: any): RRData | null => {
  if (!drawing.rrData) {
    // Legacy conversion from points-based structure
    if (!drawing.points || drawing.points.length < 3) return null;

    const [entryPoint, stopPoint, targetPoint] = drawing.points;
    const timeStart = new Date().toISOString();
    const timeEnd = new Date(Date.now() + 6 * 60 * 60 * 1000).toISOString(); // 6 hours later

    return {
      entry: entryPoint.price,
      stop: stopPoint.price,
      target: targetPoint.price,
      direction: targetPoint.price > entryPoint.price ? 'long' : 'short',
      positionSize: drawing.positionSize || undefined,
      timeStart,
      timeEnd
    };
  }

  return drawing.rrData;
};

export const createRiskRewardSeries = (
  drawing: any,
  index: number,
  onEdit?: (index: number) => void,
  isDraggable: boolean = true,
  onStartDrag?: (event: any, drawingIndex: number, dragType: 'tool' | 'handle', pointIndex?: number) => void,
  onUpdateDrawing?: (index: number, updatedDrawing: any) => void
) => {
  const rrData = convertDrawingToRRData(drawing);
  if (!rrData) return [];

  const style = drawing.style || {};
  const { risk, reward, rr, dollarRisk, dollarReward } = calculateRiskReward(rrData);

  // Colors based on direction and zones
  const entryColor = style.entryColor || '#ffffff';
  const riskColor = style.riskColor || '#ff4757';
  const rewardColor = style.rewardColor || '#00e7b6';
  const lineWidth = style.width || 2;
  const showLabels = style.showLabels !== false;
  const showRatio = style.showRatio !== false;

  const series = [];

  // Time boundaries for the RR box
  const leftTime = rrData.timeStart;
  const rightTime = rrData.timeEnd;
  const centerTime = new Date((new Date(leftTime).getTime() + new Date(rightTime).getTime()) / 2).toISOString();

  // Create simple, functional RR tool like TradingView

  // 1. Entry line (horizontal)
  series.push({
    name: `RR_${index}_entry_line`,
    type: 'line',
    coordinateSystem: 'cartesian2d',
    data: [[leftTime, rrData.entry], [rightTime, rrData.entry]],
    lineStyle: {
      color: entryColor,
      width: lineWidth + 1,
      type: 'solid'
    },
    symbol: 'none',
    z: 1000,
    silent: false,
    animation: false
  });

  // 2. Stop line (horizontal)
  series.push({
    name: `RR_${index}_stop_line`,
    type: 'line',
    coordinateSystem: 'cartesian2d',
    data: [[leftTime, rrData.stop], [rightTime, rrData.stop]],
    lineStyle: {
      color: riskColor,
      width: lineWidth,
      type: 'solid'
    },
    symbol: 'none',
    z: 1000,
    silent: false,
    animation: false
  });

  // 3. Target line (horizontal)
  series.push({
    name: `RR_${index}_target_line`,
    type: 'line',
    coordinateSystem: 'cartesian2d',
    data: [[leftTime, rrData.target], [rightTime, rrData.target]],
    lineStyle: {
      color: rewardColor,
      width: lineWidth,
      type: 'solid'
    },
    symbol: 'none',
    z: 1000,
    silent: false,
    animation: false
  });

  // 4. Left vertical border
  const minPrice = Math.min(rrData.entry, rrData.stop, rrData.target);
  const maxPrice = Math.max(rrData.entry, rrData.stop, rrData.target);

  series.push({
    name: `RR_${index}_left_border`,
    type: 'line',
    coordinateSystem: 'cartesian2d',
    data: [[leftTime, minPrice], [leftTime, maxPrice]],
    lineStyle: {
      color: entryColor,
      width: lineWidth,
      type: 'solid'
    },
    symbol: 'none',
    z: 1000,
    silent: false,
    animation: false
  });

  // 5. Right vertical border
  series.push({
    name: `RR_${index}_right_border`,
    type: 'line',
    coordinateSystem: 'cartesian2d',
    data: [[rightTime, minPrice], [rightTime, maxPrice]],
    lineStyle: {
      color: entryColor,
      width: lineWidth,
      type: 'solid'
    },
    symbol: 'none',
    z: 1000,
    silent: false,
    animation: false
  });

  // 6. Add draggable handles if enabled
  if (isDraggable) {
    const handleSize = 8;

    // Entry handle (center, moves all levels together)
    series.push({
      name: `RR_${index}_entry_handle`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[centerTime, rrData.entry]],
      symbol: 'circle',
      symbolSize: handleSize + 2,
      itemStyle: {
        color: entryColor,
        borderColor: '#ffffff',
        borderWidth: 2,
        shadowColor: 'rgba(0, 0, 0, 0.3)',
        shadowBlur: 4
      },
      z: 2001,
      silent: false,
      animation: false,
      cursor: 'move',
      drawingIndex: index,
      handleType: 'entry',
      drawingType: 'rr_level_handle',
      isDraggableHandle: true
    });

    // Stop handle (adjusts stop level only)
    series.push({
      name: `RR_${index}_stop_handle`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[centerTime, rrData.stop]],
      symbol: 'circle',
      symbolSize: handleSize,
      itemStyle: {
        color: riskColor,
        borderColor: '#ffffff',
        borderWidth: 2,
        shadowColor: 'rgba(0, 0, 0, 0.3)',
        shadowBlur: 4
      },
      z: 2001,
      silent: false,
      animation: false,
      cursor: 'ns-resize',
      drawingIndex: index,
      handleType: 'stop',
      drawingType: 'rr_level_handle',
      isDraggableHandle: true
    });

    // Target handle (adjusts target level only)
    series.push({
      name: `RR_${index}_target_handle`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[centerTime, rrData.target]],
      symbol: 'circle',
      symbolSize: handleSize,
      itemStyle: {
        color: rewardColor,
        borderColor: '#ffffff',
        borderWidth: 2,
        shadowColor: 'rgba(0, 0, 0, 0.3)',
        shadowBlur: 4
      },
      z: 2001,
      silent: false,
      animation: false,
      cursor: 'ns-resize',
      drawingIndex: index,
      handleType: 'target',
      drawingType: 'rr_level_handle',
      isDraggableHandle: true
    });

    // Corner resize handles for adjusting box width
    series.push({
      name: `RR_${index}_left_resize`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[leftTime, rrData.entry]],
      symbol: 'rect',
      symbolSize: 6,
      itemStyle: {
        color: entryColor,
        borderColor: '#ffffff',
        borderWidth: 1
      },
      z: 2000,
      silent: false,
      animation: false,
      cursor: 'ew-resize',
      drawingIndex: index,
      handleType: 'resize_left',
      drawingType: 'rr_resize_handle',
      isDraggableHandle: true
    });

    series.push({
      name: `RR_${index}_right_resize`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[rightTime, rrData.entry]],
      symbol: 'rect',
      symbolSize: 6,
      itemStyle: {
        color: entryColor,
        borderColor: '#ffffff',
        borderWidth: 1
      },
      z: 2000,
      silent: false,
      animation: false,
      cursor: 'ew-resize',
      drawingIndex: index,
      handleType: 'resize_right',
      drawingType: 'rr_resize_handle',
      isDraggableHandle: true
    });
  }

  // 7. Add labels for prices and R:R ratio
  if (showLabels) {
    // Entry label
    series.push({
      name: `RR_${index}_entry_label`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[rightTime, rrData.entry]],
      symbol: 'none',
      label: {
        show: true,
        position: 'right',
        formatter: `Entry: ${rrData.entry.toFixed(4)}`,
        color: entryColor,
        fontSize: 11,
        fontWeight: 'bold',
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        padding: [2, 6],
        borderRadius: 3
      },
      z: 1001,
      silent: true,
      animation: false
    });

    // Stop label
    series.push({
      name: `RR_${index}_stop_label`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[rightTime, rrData.stop]],
      symbol: 'none',
      label: {
        show: true,
        position: 'right',
        formatter: `Stop: ${rrData.stop.toFixed(4)}`,
        color: riskColor,
        fontSize: 11,
        fontWeight: 'bold',
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        padding: [2, 6],
        borderRadius: 3
      },
      z: 1001,
      silent: true,
      animation: false
    });

    // Target label
    series.push({
      name: `RR_${index}_target_label`,
      type: 'scatter',
      coordinateSystem: 'cartesian2d',
      data: [[rightTime, rrData.target]],
      symbol: 'none',
      label: {
        show: true,
        position: 'right',
        formatter: `Target: ${rrData.target.toFixed(4)}`,
        color: rewardColor,
        fontSize: 11,
        fontWeight: 'bold',
        backgroundColor: 'rgba(0, 0, 0, 0.7)',
        padding: [2, 6],
        borderRadius: 3
      },
      z: 1001,
      silent: true,
      animation: false
    });

    // R:R ratio label (center top)
    if (showRatio) {
      series.push({
        name: `RR_${index}_ratio_label`,
        type: 'scatter',
        coordinateSystem: 'cartesian2d',
        data: [[centerTime, Math.max(rrData.entry, rrData.stop, rrData.target)]],
        symbol: 'none',
        label: {
          show: true,
          position: 'top',
          formatter: `R:R ${rr.toFixed(2)}`,
          color: '#ffffff',
          fontSize: 12,
          fontWeight: 'bold',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          padding: [4, 8],
          borderRadius: 4
        },
        z: 1002,
        silent: true,
        animation: false
      });
    }
  }
  return series;
};



  return series;
};

// Helper function to create a new RR tool
export const createNewRRTool = (
  entryPrice: number,
  entryTime: string,
  direction: 'long' | 'short' = 'long',
  positionSize?: number
): any => {
  const stopOffset = direction === 'long' ? entryPrice * 0.02 : entryPrice * 0.02; // 2% default
  const targetOffset = direction === 'long' ? entryPrice * 0.06 : entryPrice * 0.06; // 6% default

  const stop = direction === 'long' ? entryPrice - stopOffset : entryPrice + stopOffset;
  const target = direction === 'long' ? entryPrice + targetOffset : entryPrice - targetOffset;

  const timeStart = entryTime;
  const timeEnd = new Date(new Date(entryTime).getTime() + 6 * 60 * 60 * 1000).toISOString(); // 6 hours later

  return {
    type: 'rr',
    id: Date.now(),
    rrData: {
      entry: entryPrice,
      stop,
      target,
      direction,
      positionSize,
      timeStart,
      timeEnd
    },
    style: {
      entryColor: '#ffffff',
      riskColor: '#ff4757',
      rewardColor: '#00e7b6',
      width: 2,
      showLabels: true,
      showRatio: true
    }
  };
};

// Helper function to update RR tool data
export const updateRRTool = (
  drawing: any,
  updates: Partial<RRData>
): any => {
  const currentRRData = convertDrawingToRRData(drawing);
  if (!currentRRData) return drawing;

  const updatedRRData = { ...currentRRData, ...updates };

  // Auto-detect direction if entry, stop, or target changed
  if (updates.entry !== undefined || updates.stop !== undefined || updates.target !== undefined) {
    updatedRRData.direction = updatedRRData.target > updatedRRData.entry ? 'long' : 'short';
  }

  return {
    ...drawing,
    rrData: updatedRRData
  };
};

// Helper function to handle drag updates for RR tools
export const handleRRDrag = (
  drawing: any,
  handleType: string,
  newPrice: number,
  newTime?: string
): any => {
  const rrData = convertDrawingToRRData(drawing);
  if (!rrData) return drawing;

  switch (handleType) {
    case 'entry':
      // Move all levels together (maintain relative distances)
      const entryDelta = newPrice - rrData.entry;
      return updateRRTool(drawing, {
        entry: newPrice,
        stop: rrData.stop + entryDelta,
        target: rrData.target + entryDelta
      });

    case 'stop':
      // Move only stop level
      return updateRRTool(drawing, { stop: newPrice });

    case 'target':
      // Move only target level
      return updateRRTool(drawing, { target: newPrice });

    case 'resize':
      // Handle box resizing (adjust time boundaries)
      if (newTime) {
        // This would need more complex logic based on which corner is being dragged
        // For now, just update the time boundaries
        return updateRRTool(drawing, {
          timeStart: newTime < rrData.timeEnd ? newTime : rrData.timeStart,
          timeEnd: newTime > rrData.timeStart ? newTime : rrData.timeEnd
        });
      }
      break;

    default:
      return drawing;
  }

  return drawing;
};

const RiskRewardTool: React.FC<RiskRewardToolProps> = ({ drawing, index, onEdit, onDelete }) => {
  // This component is mainly for type definitions and utility functions
  // The actual rendering is handled by createRiskRewardSeries function
  return null;
};

export default RiskRewardTool;
